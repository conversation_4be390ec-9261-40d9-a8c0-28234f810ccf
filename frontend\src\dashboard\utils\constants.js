// src/dashboard/utils/constants.js

/**
 * User plan constants
 */
export const USER_PLANS = {
  STARTER: 'Starter',
  PRO: 'Pro',
  ENTERPRISE: 'Enterprise'
};

/**
 * Admin roles
 */
export const ADMIN_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MODERATOR: 'moderator'
};

/**
 * User status constants for filtering
 */
export const USER_STATUS = {
  VERIFIED: 'verified',
  UNVERIFIED: 'unverified',
  ACTIVE: 'active',
  INACTIVE: 'inactive'
};

/**
 * System health status
 */
export const SYSTEM_HEALTH = {
  HEALTHY: 'healthy',
  WARNING: 'warning',
  CRITICAL: 'critical',
  UNKNOWN: 'unknown'
};

/**
 * Activity types for UserActivity logging
 * This is the corrected and completed list.
 */
export const ACTIVITY_TYPES = {
  // User activities
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTRATION: 'registration',
  EMAIL_VERIFICATION: 'email_verification',
  PASSWORD_CHANGE: 'password_change',
  PROFILE_UPDATE: 'profile_update',
  SUBSCRIPTION_CHANGE: 'subscription_change',
  FILE_UPLOAD: 'file_upload',
  FILE_DOWNLOAD: 'file_download',
  AI_CHAT_MESSAGE: 'ai_chat_message',
  BUSINESS_PLAN_GENERATION: 'business_plan_generation',
  INVESTOR_PITCH_GENERATION: 'investor_pitch_generation',
  BUSINESS_QA_QUERY: 'business_qa_query',
  PDF_ANALYSIS: 'pdf_analysis',
  LIMIT_EXCEEDED: 'limit_exceeded',
  ERROR_OCCURRED: 'error_occurred',
  
  // Admin actions on users
  USER_DELETION: 'user_deletion', // FIX: Added the missing type
  LIMIT_UPDATE: 'limit_update',
  BULK_USER_UPDATE: 'bulk_user_update',

  // Admin-specific activities
  ADMIN_LOGIN: 'admin_login',
  ADMIN_CREATION: 'admin_creation',
  ADMIN_UPDATE: 'admin_update',
  ADMIN_DELETION: 'admin_deletion',
  ADMIN_PASSWORD_CHANGE: 'admin_password_change',

  // System settings activities
  SYSTEM_SETTINGS_UPDATE: 'system_settings_update',
  DEFAULT_LIMITS_UPDATE: 'default_limits_update',
  SYSTEM_CONFIG_UPDATE: 'system_config_update',
  RATE_LIMITING_UPDATE: 'rate_limiting_update',
  SECURITY_SETTINGS_UPDATE: 'security_settings_update',
  FEATURE_FLAGS_UPDATE: 'feature_flags_update',
  SYSTEM_SETTINGS_RESET: 'system_settings_reset'
};

/**
 * Permission types for admin roles
 */
export const PERMISSIONS = {
  USER_MANAGEMENT: 'userManagement',
  LIMITS_MANAGEMENT: 'limitsManagement',
  SYSTEM_SETTINGS: 'systemSettings',
  ANALYTICS: 'analytics',
  ADMIN_MANAGEMENT: 'adminManagement'
};

/**
 * Default pagination settings
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  LIMIT_OPTIONS: [10, 20, 50, 100]
};

/**
 * API endpoints for the dashboard
 */
export const API_ENDPOINTS = {
  // Dashboard
  DASHBOARD_OVERVIEW: '/api/dashboard/overview',
  SYSTEM_ANALYTICS: '/api/dashboard/analytics',
  
  // Users
  USERS: '/api/dashboard/users',
  USER_DETAILS: (id) => `/api/dashboard/users/${id}`,
  USER_LIMITS: (id) => `/api/dashboard/users/${id}/limits`,
  BULK_UPDATE_USERS: '/api/dashboard/users/bulk-update',
  
  // Admins
  ADMIN_LOGIN: '/api/dashboard/admins/auth/login',
  ADMIN_ME: '/api/dashboard/admins/auth/me',
  ADMINS: '/api/dashboard/admins',
  ADMIN_DETAILS: (id) => `/api/dashboard/admins/${id}`,
  
  // Settings
  SYSTEM_SETTINGS: '/api/dashboard/settings'
};

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  ADMIN_TOKEN: 'adminToken',
  ADMIN_DATA: 'adminData'
};