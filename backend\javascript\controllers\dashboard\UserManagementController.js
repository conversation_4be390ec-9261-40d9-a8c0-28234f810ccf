// controllers/dashboard/UserManagementController.js
import User from '../../models/User.js';
import { UserActivity } from '../../models/dashboard/index.js';
import { ACTIVITY_TYPES } from '../../utils/constants.js';

/**
 * @desc    Get all users with pagination and filtering
 * @route   GET /api/dashboard/users
 * @access  Private (Admin with userManagement permission)
 */
export const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      planFilter = '',
      statusFilter = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build filter query
    const filter = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (planFilter) {
      filter['subscription.planName'] = planFilter;
    }

    if (statusFilter) {
      if (statusFilter === 'verified') filter.isVerified = true;
      else if (statusFilter === 'unverified') filter.isVerified = false;
      else if (statusFilter === 'active') filter['subscription.status'] = 'active';
      else if (statusFilter === 'inactive') filter['subscription.status'] = { $ne: 'active' };
    }

    // Build sort object
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const users = await User.find(filter)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .lean();

    const totalUsers = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalUsers / limitNum);

    const enhancedUsers = await Promise.all(
      users.map(async (user) => {
        const activityCount = await UserActivity.countDocuments({
          userId: user._id,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
        });

        return {
          ...user,
          id: user._id.toString(),
          activityCount30Days: activityCount
        };
      })
    );

    res.json({
      success: true,
      data: {
        users: enhancedUsers,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalUsers,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      }
    });

  } catch (error) {
    console.error('Error in getAllUsers:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching users.' 
    });
  }
};

/**
 * @desc    Get single user details
 * @route   GET /api/dashboard/users/:id
 * @access  Private (Admin with userManagement permission)
 */
export const getUserDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id).select('-password').lean();

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found.' });
    }

    res.json({
      success: true,
      data: {
        user: { ...user, id: user._id.toString() }
      }
    });

  } catch (error) {
    console.error('Error in getUserDetails:', error);
    res.status(500).json({ success: false, error: 'Server error while fetching user details.' });
  }
};

/**
 * @desc    Update user details
 * @route   PUT /api/dashboard/users/:id
 * @access  Private (Admin with userManagement permission)
 */
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    delete updates.password;

    const user = await User.findByIdAndUpdate(id, { $set: updates }, { new: true }).select('-password');

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found.' });
    }

    await UserActivity.create({
      userId: user._id,
      activityType: ACTIVITY_TYPES.PROFILE_UPDATE,
      metadata: { updatedBy: req.admin.id, updatedFields: Object.keys(updates) },
      success: true
    });

    res.json({ success: true, message: 'User updated successfully.', data: { user } });

  } catch (error) {
    console.error('Error in updateUser:', error);
    res.status(500).json({ success: false, error: 'Server error while updating user.' });
  }
};

/**
 * @desc    Delete user
 * @route   DELETE /api/dashboard/users/:id
 * @access  Private (Admin with userManagement permission)
 */
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found.' });
    }

    // Log the deletion action *before* deleting the user
    await UserActivity.create({
      userId: user._id, // FIX: Provide the user's ID for the log
      activityType: ACTIVITY_TYPES.USER_DELETION, // FIX: Use the valid activity type
      adminId: req.admin.id, // Log which admin performed the action
      metadata: {
        deletedBy: req.admin.id,
        deletedUserEmail: user.email,
      },
      success: true
    });

    // Now, delete the user and all their associated activities
    await Promise.all([
      User.findByIdAndDelete(id),
      UserActivity.deleteMany({ userId: id })
    ]);

    res.json({ success: true, message: 'User and their activities deleted successfully.' });

  } catch (error) {
    console.error('Error in deleteUser:', error);
    res.status(500).json({ success: false, error: 'Server error while deleting user.', details: error.message });
  }
};

/**
 * @desc    Update user subscription limits
 * @route   PUT /api/dashboard/users/:id/limits
 * @access  Private (Admin with limitsManagement permission)
 */
export const updateUserLimits = async (req, res) => {
  try {
    const { id } = req.params;
    const { limits } = req.body;

    if (!limits) {
      return res.status(400).json({ success: false, error: 'Limits data is required.' });
    }

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found.' });
    }

    Object.assign(user.subscription, limits);
    await user.save();

    await UserActivity.create({
      userId: user._id,
      activityType: ACTIVITY_TYPES.LIMIT_UPDATE,
      metadata: { updatedBy: req.admin.id, newLimits: limits },
      success: true
    });

    res.json({ success: true, message: 'User limits updated successfully.', data: { subscription: user.subscription } });

  } catch (error) {
    console.error('Error in updateUserLimits:', error);
    res.status(500).json({ success: false, error: 'Server error while updating user limits.' });
  }
};

/**
 * @desc    Bulk update users
 * @route   PUT /api/dashboard/users/bulk-update
 * @access  Private (Admin with userManagement permission)
 */
export const bulkUpdateUsers = async (req, res) => {
  try {
    const { userIds, updates } = req.body;

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ success: false, error: 'User IDs array is required.' });
    }
    delete updates.password;

    const result = await User.updateMany({ _id: { $in: userIds } }, { $set: updates });

    await UserActivity.create({
      userId: null,
      activityType: ACTIVITY_TYPES.BULK_USER_UPDATE,
      metadata: { updatedBy: req.admin.id, affectedUsers: result.modifiedCount, updates: Object.keys(updates) },
      success: true
    });

    res.json({ success: true, message: `${result.modifiedCount} users updated successfully.`, data: result });

  } catch (error) {
    console.error('Error in bulkUpdateUsers:', error);
    res.status(500).json({ success: false, error: 'Server error while bulk updating users.' });
  }
};